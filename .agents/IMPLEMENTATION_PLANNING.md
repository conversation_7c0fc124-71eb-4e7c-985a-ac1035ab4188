# Implementation Planning

Guidelines for breaking down complex work into manageable, incremental stages.

## Philosophy

- **Incremental progress over big bangs** - Small changes that compile and pass tests
- **Learning from existing code** - Study and plan before implementing
- **Clear intent over clever code** - Be boring and obvious

## Planning Process

### 1. Study Existing Patterns

Before planning implementation:

- Find 3 similar features/components in the codebase
- Identify common patterns and conventions
- Note libraries/utilities already in use
- Study existing test patterns

### 2. Break Into Stages

Complex work should be broken into **3-5 stages**. Each stage should:

- Be completable in a reasonable timeframe
- Result in working, testable code
- Build incrementally toward the final goal
- Be independently valuable if possible

### 3. Document Your Plan

Create `IMPLEMENTATION_PLAN.md` in your working directory:

```markdown
# Implementation Plan: [Feature Name]

## Overview

[Brief description of what you're building and why]

## Stage 1: [Name]

**Goal**: [Specific deliverable]
**Success Criteria**: [Testable outcomes]
**Tests**: [Specific test cases to write]
**Status**: [Not Started|In Progress|Complete]

## Stage 2: [Name]

**Goal**: [Specific deliverable]
**Success Criteria**: [Testable outcomes]
**Tests**: [Specific test cases to write]
**Status**: [Not Started|In Progress|Complete]

## Stage 3: [Name]

**Goal**: [Specific deliverable]
**Success Criteria**: [Testable outcomes]
**Tests**: [Specific test cases to write]
**Status**: [Not Started|In Progress|Complete]

[Continue for additional stages...]

## Notes

[Any important considerations, dependencies, or decisions]
```

### 4. Implementation Flow Per Stage

For each stage:

1. **Understand** - Study existing patterns in codebase
2. **Test** - Write test first (red)
3. **Implement** - Minimal code to pass (green)
4. **Refactor** - Clean up with tests passing
5. **Commit** - With clear message linking to plan
6. **Update Status** - Mark stage complete in plan

### 5. Plan Maintenance

- Update status as you progress through stages
- Add notes about decisions or discoveries
- Adjust later stages based on learnings
- Remove `IMPLEMENTATION_PLAN.md` when all stages complete

## Quality Standards

### Every Stage Must

- [ ] Compile successfully
- [ ] Pass all existing tests
- [ ] Include tests for new functionality
- [ ] Follow project formatting/linting
- [ ] Have clear commit message explaining "why"

### Before Moving to Next Stage

- [ ] Current stage meets all success criteria
- [ ] All tests are passing
- [ ] Code has been self-reviewed
- [ ] No linter/formatter warnings
- [ ] Plan document is updated

## Decision Framework

When multiple approaches exist, choose based on:

1. **Testability** - Can I easily test this?
2. **Readability** - Will someone understand this in 6 months?
3. **Consistency** - Does this match project patterns?
4. **Simplicity** - Is this the simplest solution that works?
5. **Reversibility** - How hard to change later?

## Example Stage Breakdown

### Feature: Add User Authentication

**Stage 1: Basic Auth Structure**

- Goal: Set up auth middleware and basic user model
- Success Criteria: Can create user, middleware blocks unauthenticated requests
- Tests: User creation, middleware blocking, middleware allowing

**Stage 2: Login/Logout**

- Goal: Implement login and logout endpoints
- Success Criteria: Users can log in with valid credentials, sessions work
- Tests: Valid login, invalid login, logout, session persistence

**Stage 3: Protected Routes**

- Goal: Protect existing routes with authentication
- Success Criteria: All sensitive routes require authentication
- Tests: Protected routes block anonymous, allow authenticated

This approach ensures each stage delivers working functionality while building toward the complete feature.
