# Development Workflow

Complete workflow guidance for the mcp-server-odi project, from planning through deployment.

## Phase 1: Pre-Implementation (Planning)

### Understanding & Planning

- **Fully understand the task** before making any edits
- If you don't understand or are unsure about something, **propose options and ask** before implementation
- **Show your plan** before implementation
- **Reuse existing code and logic** as much as possible
- **Follow existing coding standards**

### Complex Work Planning

For complex features, use staged implementation:

- Break work into **3-5 manageable stages**
- Create `IMPLEMENTATION_PLAN.md` with specific deliverables
- Each stage should result in working, testable code
- See [IMPLEMENTATION_PLANNING.md](./IMPLEMENTATION_PLANNING.md) for detailed guidance

### Testing Considerations

- **Always consider testability** during planning
- Plan to **add tests for new features**
- Identify how tests should be **run before commit** or add them to pre-commit/pre-push hooks

## Phase 2: Implementation (Editing)

### Implementation Flow (Per Stage)

1. **Understand** - Study existing patterns in codebase
2. **Test** - Write test first (red)
3. **Implement** - Minimal code to pass (green)
4. **Refactor** - Clean up with tests passing
5. **Commit** - With clear message linking to plan

### Incremental Progress Principles

- **Small changes that compile and pass tests**
- **Commit working code frequently** - don't wait for perfection
- **Each commit should be independently valuable**
- **Update implementation plan status as you progress**

### When Stuck

Follow the **3 attempts rule**:

- Maximum 3 attempts per issue, then STOP
- Document what failed and research alternatives
- See [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) for systematic approach

### File Organization

- **Don't do too much file generation** - organize code in existing files or folders
- Use `local-only/` folder for temporary docs/scripts
- Use `readonly/` folder to learn from, but **read only**
- Use `local-only/prompts.md` to record prompts
- Use `local-only/learning.md` to record learnings for future reference
  - Update with key technical insights, solutions, and patterns discovered

### Version Control Awareness

- For `local-only/` files/paths: **git ignore them**
- Be **very cautious** when changing them since they are not version managed

## Phase 3: Code Review

Review the changes everytime you made a PR.

Follow comprehensive code review guidelines detailed in [CODE_REVIEW.md](./CODE_REVIEW.md).

### Key Review Areas

- **Design & Architecture** - Overall design and system integration
- **Functionality & Testing** - Correctness, edge cases, and test coverage
- **Code Quality** - Complexity, naming, comments, and documentation
- **Context & Health** - Broad impact and system health improvement

## Phase 4: Post-Implementation (Testing & Deployment)

### Testing

- **Add tests for new features**
- **Run tests** before committing code or add them to pre-commit/pre-push

### Committing

- **Commit code with less than 50 words** of commit messages
- **Never bypass pre-commit errors** - `git commit --no-verify` is strictly forbidden

### Deployment

- **Push code** and use `gh` CLI to trigger build
- Build will create image and deploy to dev environment
- **Avoid pushing to main/master branch** - create PR instead

### Verification

- Usually takes **10 minutes** for deployment to take effect
- Use `argocd` commandline to check deployment status
- For dev env, check health with:
  ```bash
  curl -s https://mpp-dev-i.ingka-dt.cn/mcp-server-odi/health | jq '{status, version}'
  ```
- The version contains commit id prefix for verification

## Quality Gates

### Definition of Done (Every Commit)

- [ ] Code compiles successfully
- [ ] All existing tests pass
- [ ] New functionality has tests
- [ ] Code follows project formatting/linting
- [ ] Commit message explains "why"
- [ ] Implementation matches plan stage
- [ ] No TODOs without issue numbers

### Before Moving to Next Stage

- [ ] Current stage meets all success criteria
- [ ] All tests are passing
- [ ] Code has been self-reviewed
- [ ] No linter/formatter warnings
- [ ] Plan document is updated

## Key Principles

1. **Incremental progress over big bangs** - Small working changes
2. **Learning from existing code** - Study before implementing
3. **Understand first, implement second**
4. **Test everything** - Tests are part of the feature
5. **Never bypass safety checks** - No `--no-verify` commits
6. **Carefully review changes** - Quality is built in
7. **Verify deployments work** - Complete the feedback loop
