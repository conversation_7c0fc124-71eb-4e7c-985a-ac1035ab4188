# Troubleshooting Guidelines

Systematic approach for when you're stuck, including the critical "3 attempts rule."

## The 3 Attempts Rule

**CRITICAL**: Maximum 3 attempts per issue, then STOP and reassess.

This prevents the common trap of endlessly trying the same approach and accumulating technical debt.

## When Stuck: 4-Step Process

### Step 1: Document What Failed

After your 3rd failed attempt, document:

- **What you tried**: Specific approaches, code changes, configurations
- **Specific error messages**: Full stack traces, compiler errors, test failures
- **Why you think it failed**: Your hypothesis about the root cause
- **Time spent**: How long you've been working on this specific issue

### Step 2: Research Alternatives

Find 2-3 similar implementations:

- **In the current codebase**: How do similar features work?
- **In documentation**: Official docs, API references, examples
- **In the community**: Stack Overflow, GitHub issues, blog posts
- **Note different approaches**: What patterns do they use?

### Step 3: Question Fundamentals

Ask these critical questions:

- **Is this the right abstraction level?** Maybe you're solving at the wrong layer
- **Can this be split into smaller problems?** Break it down further
- **Is there a simpler approach entirely?** Remove complexity instead of adding
- **Am I fighting the framework?** Maybe there's a more idiomatic way
- **Do I understand the problem correctly?** Re-read requirements

### Step 4: Try Different Angle

Consider these alternative approaches:

- **Different library/framework feature**: Use a different API or tool
- **Different architectural pattern**: MVC vs. functional vs. event-driven
- **Remove abstraction**: Sometimes the simple, direct approach is better
- **Change the interface**: Maybe the problem is in how you're approaching it
- **Ask for help**: Bring your documentation to a colleague or community

## Documentation Template

When documenting failures, use this template:

```markdown
## Issue: [Brief Description]

### Attempts Made

1. **Approach**: [What you tried]
   - **Error**: [Specific error message]
   - **Hypothesis**: [Why you think it failed]

2. **Approach**: [What you tried]
   - **Error**: [Specific error message]
   - **Hypothesis**: [Why you think it failed]

3. **Approach**: [What you tried]
   - **Error**: [Specific error message]
   - **Hypothesis**: [Why you think it failed]

### Research Done

- **Similar implementations found**: [Links/descriptions]
- **Different approaches noted**: [What patterns they use]
- **Documentation consulted**: [Links to docs, APIs, etc.]

### Fundamental Questions

- **Right abstraction level?**: [Your assessment]
- **Can be split smaller?**: [Potential breakdown]
- **Simpler approach exists?**: [Alternative ideas]

### Next Steps

- [ ] Try approach: [Specific next attempt]
- [ ] Ask for help from: [Person/community]
- [ ] Research: [Specific area to investigate]
```

## Common Patterns When Stuck

### Fighting the Framework

**Symptoms**: Lots of workarounds, complex configuration, "hacks"
**Solution**: Step back and find the idiomatic way

### Wrong Abstraction Level

**Symptoms**: Code feels forced, lots of type casting, complex interfaces
**Solution**: Move up or down a level of abstraction

### Premature Optimization

**Symptoms**: Complex caching, performance tweaks, clever algorithms
**Solution**: Make it work first, optimize later with measurements

### Over-Engineering

**Symptoms**: Lots of interfaces, complex inheritance, "future-proofing"
**Solution**: Solve the current problem simply

## Prevention Strategies

### Before Starting

- [ ] Study 3 similar implementations in the codebase
- [ ] Read relevant documentation thoroughly
- [ ] Understand the problem requirements clearly
- [ ] Plan your approach (see IMPLEMENTATION_PLANNING.md)

### During Implementation

- [ ] Make small, incremental changes
- [ ] Test frequently
- [ ] Commit working code regularly
- [ ] Keep track of what you've tried

### When Feeling Stuck

- [ ] Take a break (seriously, walk away for 15 minutes)
- [ ] Explain the problem to someone else (rubber duck debugging)
- [ ] Write down what you're trying to accomplish
- [ ] Question your assumptions

## Recovery Strategies

### After Hitting the 3 Attempts Limit

1. **Take a longer break**: Sometimes perspective comes with time
2. **Start over with a simpler approach**: Throw away complex attempts
3. **Pair with someone**: Fresh eyes see different solutions
4. **Change the problem**: Maybe the requirements can be adjusted
5. **Learn something new**: Sometimes you need a new tool or technique

### Signs You Need Help

- You've been stuck on the same issue for more than 2 hours
- You're making the same type of error repeatedly
- You're adding complexity to solve complexity
- You're considering disabling tests or bypassing safety checks
- You're frustrated and making careless mistakes

Remember: Getting stuck is normal. The key is recognizing when to stop digging and start thinking differently.
