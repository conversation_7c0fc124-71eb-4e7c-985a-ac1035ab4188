#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-push checks..."

# 1. Full build test
echo "🏗️ Running full build test..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Build failed. Cannot push broken code."
  exit 1
fi

# 2. Run all tests with coverage
echo "🧪 Running full test suite..."
npm run test:coverage 2>/dev/null || npm test
if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Cannot push failing code."
  exit 1
fi

# 3. Run fast blackbox tests (CRITICAL for production readiness)
echo "🔍 Running fast blackbox tests..."
make test-blackbox-fast
if [ $? -ne 0 ]; then
  echo "❌ Fast blackbox tests failed. Cannot push failing integration."
  exit 1
fi

# 4. Security audit
echo "🔒 Running security audit..."
npm audit --audit-level=high
if [ $? -ne 0 ]; then
  echo "⚠️ Security vulnerabilities found. Consider fixing before push."
  echo "💡 Run 'npm audit fix' to automatically fix issues."
  # Don't fail on audit issues, just warn
fi

# 5. Check for TODO/FIXME comments in staged files
echo "📝 Checking for TODO/FIXME comments..."
# Only check files that exist (not deleted files)
STAGED_FILES=$(git diff --cached --name-only --diff-filter=AM)
if [ -n "$STAGED_FILES" ]; then
  echo "$STAGED_FILES" | xargs grep -l "TODO\|FIXME" 2>/dev/null
  if [ $? -eq 0 ]; then
    echo "⚠️ Found TODO/FIXME comments in staged files. Consider addressing them."
    # Don't fail, just warn
  fi
fi

echo "✅ All pre-push checks passed! Ready to push."
