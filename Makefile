.PHONY: help test-blackbox test-blackbox-fast test-blackbox-full test-blackbox-oauth clean

help: ## Show help
	@echo "🧪 MCP Server ODI - Reorganized Test Structure"
	@echo "===================================================="
	@echo ""
	@echo "🎯 Main Testing Commands:"
	@echo "  npm test                      Run type check + unit + simple integration tests"
	@echo "  npm run test:unit            Run unit tests only (no server needed)"
	@echo "  npm run test:integration:simple Run simple integration tests"
	@echo "  make test-blackbox             Run full blackbox tests with Docker Compose"
	@echo "  make test-blackbox:fast       Run fast blackbox tests (~30s)"
	@echo "  make test-blackbox:full       Run comprehensive blackbox tests (~3-5min)"
	@echo "  make test-blackbox:oauth      Run OAuth-specific blackbox tests (~2min)"
	@echo "  make clean                    Clean up Docker resources"
	@echo ""
	@echo "📦 Test Categories:"
	@echo "  🔹 Unit Tests:              Jest tests for individual components"
	@echo "  🔹 Simple Integration:     Jest tests with minimal external dependencies"
	@echo "  🔧 Blackbox Tests:          Docker Compose + mocked dependencies + API testing"
	@echo ""
	@echo "💡 Tip: Use 'npm test' for development, 'make test-blackbox' for CI/CD"

# Blackbox Tests (Docker Compose with mocked dependencies)
test-blackbox-fast: ## Run fast blackbox test with Docker Compose (~30s)
	@echo "⚡ Running fast blackbox test with mocked dependencies..."
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml up --build -d
	@echo "⏳ Waiting for services to start..."
	@sleep 30
	@MCP_SERVER_URL=http://localhost:3000 node blackbox/tests/docker-test-runner.js --fast
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml down -v --remove-orphans

test-blackbox-full: ## Run full blackbox test suite with Docker Compose (~3-5min)
	@echo "🧪 Running full blackbox test suite with mocked dependencies..."
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml -f blackbox/config/docker-compose.oauth.yml up --build --abort-on-container-exit
	@sleep 20
	@MCP_SERVER_URL=http://localhost:3000 MCP_SERVER_OAUTH_URL=http://localhost:3001 node blackbox/tests/blackbox-test-runner.js --full
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml -f blackbox/config/docker-compose.oauth.yml down -v --remove-orphans

test-blackbox-oauth: ## Run OAuth-specific blackbox tests (~2min)
	@echo "🔐 Running OAuth blackbox tests with mocked dependencies..."
	@docker-compose -f blackbox/config/docker-compose.oauth.yml up --build --abort-on-container-exit
	@sleep 15
	@MCP_SERVER_URL=http://localhost:3001 node blackbox/tests/blackbox-test-runner.js --oauth
	@docker-compose -f blackbox/config/docker-compose.oauth.yml down -v --remove-orphans the working MCP client test
# Clean up Docker resources
clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up Docker resources..."
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml down -v --remove-orphans 2>/dev/null || true
	@docker-compose -f blackbox/config/docker-compose.oauth.yml down -v --remove-orphans 2>/dev/null || true
	@docker network rm mcp-blackbox-network 2>/dev/null || true
	@docker system prune -f --volumes 2>/dev/null || true

# Development utilities
logs: ## Show Docker service logs
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml logs

status: ## Show Docker service status
	@docker-compose -f blackbox/config/docker-compose.blackbox.yml ps

summary: ## Show available commands
	@echo "📊 MCP Server ODI - Available Commands"
	@echo "====================================="
	@echo ""
	@echo "🎯 Main Testing Commands:"
	@echo "  npm test                      # Run type check + unit + simple integration tests"
	@echo "  npm run test:unit            # Run unit tests only (no server needed)"
	@echo "  npm run test:integration:simple # Run simple integration tests"
	@echo "  make test-blackbox-fast       # Run fast blackbox tests (~30s)"
	@echo "  make test-blackbox-full       # Run comprehensive blackbox tests (~3-5min)"
	@echo "  make test-blackbox-oauth      # Run OAuth-specific blackbox tests (~2min)"
	@echo "  make clean                    # Clean up Docker resources"
	@echo ""
	@echo "📦 Test Categories:"
	@echo "  🔹 Unit Tests:              Jest tests for individual components"
	@echo "  🔹 Simple Integration:     Jest tests with minimal external dependencies"
	@echo "  🔧 Blackbox Tests:          Docker Compose + mocked dependencies + API testing"
	@echo ""
	@echo "💡 Tip: Use 'npm test' for development, 'make test-blackbox' for CI/CD"

# Individual Feature Tests (for development debugging)
test-ping: ## Test MCP ping implementation and health endpoints
	@echo "🏓 Testing MCP ping and health endpoints..."
	@npx jest tests/transport/http-transport.test.ts --testNamePattern="Ping|Health|Ready"

test-event-store: ## Test event store and resumability features
	@echo "💾 Testing event store and resumability..."
	@npx jest tests/utils/event-store.test.ts

test-tool-annotations: ## Test tool annotation and classification logic
	@echo "🏷️  Testing tool annotations and classification..."
	@npx jest tests/server/tool-annotations.test.ts
