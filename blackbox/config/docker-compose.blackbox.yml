# 🧪 MCP Server ODI - Blackbox Test Environment
# Docker Compose configuration with mocked dependencies for external API testing
#
# This setup provides:
# 1. MCP Server ODI (main service under test)
# 2. Mock ODS API (Order Management System)
# 3. Mock Permission Service
# 4. Mock Keycloak (for OAuth tests)
# 5. Test Runner (executes blackbox tests)
#
# Usage:
#   make test-blackbox:fast    # Fast blackbox test (~30s)
#   make test-blackbox:full    # Complete test suite (~3-5min)
#   make test-blackbox:oauth   # OAuth-specific tests (~2min)
#   make clean                  # Cleanup

version: '3.8'

services:
  # MCP Server ODI (main service under test)
  mcp-server-odi:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    env_file:
      - .env.test.local
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3000
      - OAUTH2_ENABLED=false
      - CONNECTION_AUTH_ENABLED=false
      - CONNECTION_AUTH_STRICT=false
      - PER_REQUEST_AUTH_ENABLED=false
      - PER_REQUEST_AUTH_CACHE_ENABLED=false
      - MCP_SERVER_PUBLIC_BASE_URL=http://localhost:3000
      - DEBUG_SERVICE_ADAPTER=false

      # API Endpoints (from current .env)
      - VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
      - VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
      - VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
      - VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e

      # Authentication (required even when disabled)
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=
      - X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

      # Permission Service
      - PERMISSION_SERVICE_BASE_URL=http://mock-permission-service:8081
      - PERMISSION_SERVICE_CLIENT_ID=mcp-mpc-odi
    ports:
      - "3000:3000"
    depends_on:
      - mock-ods-api
      - mock-permission-service
    # Temporarily disabled health check - using sleep in makefile instead
    # healthcheck:
    #   test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 10

  # Mock ODS API (Order Management System)
  mock-ods-api:
    image: mockserver/mockserver:latest
    container_name: mock-ods-api
    ports:
      - "8080:8080"
    volumes:
      - ./mocks:/config
    environment:
      - MOCKSERVER_WATCH_CONFIG=true
      - MOCKSERVER_DELAY_MS=100
    command: ["-m", "/config/ods-api-config.json"]
    # Temporarily disabled health check - using sleep in makefile instead
    # healthcheck:
    #   test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/mockserver/health || exit 1"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 10

  # Mock Permission Service
  mock-permission-service:
    image: mockserver/mockserver:latest
    container_name: mock-permission-service
    ports:
      - "8081:8081"
    volumes:
      - ./mocks:/config
    environment:
      - MOCKSERVER_WATCH_CONFIG=true
      - MOCKSERVER_DELAY_MS=50
    command: ["-m", "/config/permission-service-config.json"]
    # Temporarily disabled health check - using sleep in makefile instead
    # healthcheck:
    #   test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8081/mockserver/health || exit 1"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 10

  # Test Runner (executes blackbox tests)
  test-runner:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Test Runner Configuration
      - NODE_ENV=test
      - MCP_SERVER_URL=http://mcp-server-odi:3000
      - MOCK_ODS_API_URL=http://mock-ods-api:8080
      - MOCK_PERMISSION_SERVICE_URL=http://mock-permission-service:8081
      - TEST_MODE=blackbox
    volumes:
      - ../tests:/app/tests:ro
      - ./results:/app/results
    depends_on:
      - mcp-server-odi
      - mock-ods-api
      - mock-permission-service
    command: ["node", "tests/integration/blackbox-runner.js"]

networks:
  mcp-blackbox-network:
    driver: bridge

volumes:
  mocks:
    driver: local
  test-results:
    driver: local