# 🔐 MCP Server ODI - OAuth Blackbox Test Environment
# Docker Compose configuration for OAuth testing with mocked dependencies
#
# Extends base blackbox configuration with OAuth-enabled MCP server
# and additional Keycloak mocking

version: '3.8'

services:
  # OAuth-enabled MCP Server ODI
  mcp-server-odi:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    env_file:
      - .env.test.local
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3001
      - OAUTH2_ENABLED=true
      - CONNECTION_AUTH_ENABLED=false
      - CONNECTION_AUTH_STRICT=false
      - PER_REQUEST_AUTH_ENABLED=true
      - PER_REQUEST_AUTH_CACHE_ENABLED=true
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=

      # OAuth2 Configuration
      - OAUTH2_CLIENT_SECRET=test-secret-for-testing
      - KEYCLOAK_BASE_URL=http://mock-keycloak:8083/auth
      - KEYCLOAK_REALM=master
      - OAUTH2_CLIENT_ID=mcp-mpc-odi
      - OAUTH2_CLIENT_AUTH_METHOD=none
      - OAUTH2_REDIRECT_URI=http://localhost:3001/auth/callback
      - OAUTH2_SCOPES=openid,profile,email
      - MCP_SERVER_PUBLIC_BASE_URL=http://localhost:3001
      - OAUTH2_ADMIN_SCOPES=admin,roles
      - OAUTH2_WRITE_SCOPES=write
      - OAUTH2_DEFAULT_SCOPE_VALIDATION=any

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=false
      - DEBUG_OAUTH2=true
    ports:
      - "3001:3001"
    depends_on:
      - mock-ods-api
      - mock-permission-service
      - mock-keycloak
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 5s
      timeout: 3s
      retries: 10

  # Mock Keycloak (for OAuth tests)
  mock-keycloak:
    image: mockserver/mockserver:latest
    container_name: mock-keycloak
    ports:
      - "8083:8080"
    volumes:
      - ./mocks:/config
    environment:
      - MOCKSERVER_WATCH_CONFIG=true
      - MOCKSERVER_DELAY_MS=150
    command: ["-m", "/config/keycloak-config.json"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/health"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Test Runner (executes OAuth blackbox tests)
  test-runner:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Test Runner Configuration
      - NODE_ENV=test
      - MCP_SERVER_URL=http://mcp-server-odi:3001
      - MCP_SERVER_OAUTH_URL=http://mcp-server-odi:3001
      - MOCK_ODS_API_URL=http://mock-ods-api:8080
      - MOCK_PERMISSION_SERVICE_URL=http://mock-permission-service:8081
      - MOCK_KEYCLOAK_URL=http://mock-keycloak:8083
      - TEST_MODE=oauth
    volumes:
      - ../tests:/app/tests:ro
      - ./results:/app/results
    depends_on:
      mcp-server-odi:
        condition: service_healthy
      mock-ods-api:
        condition: service_healthy
      mock-permission-service:
        condition: service_healthy
      mock-keycloak:
        condition: service_healthy
    command: ["node", "tests/integration/oauth-blackbox-runner.js"]

networks:
  mcp-blackbox-network:
    external: true