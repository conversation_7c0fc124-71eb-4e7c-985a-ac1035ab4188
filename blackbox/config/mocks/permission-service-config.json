{"mockServer": {"port": 8081, "routes": [{"method": "GET", "path": "/permissions/check", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"hasPermission": true, "permissions": ["read:orders", "write:orders", "admin:orders"], "clientId": "mcp-mpc-odi", "scopes": ["orders.read", "orders.write"]}}}, {"method": "POST", "path": "/permissions/validate", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"valid": true, "permissions": ["read:orders", "write:orders"]}}}, {"method": "GET", "path": "/permissions/*", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"permissionId": "read:orders", "resource": "orders", "action": "read", "granted": true}}}]}}