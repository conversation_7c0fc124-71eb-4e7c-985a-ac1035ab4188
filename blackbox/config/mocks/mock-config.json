{"mockServer": {"port": 8080, "routes": [{"method": "GET", "path": "/api/orders", "response": {"status": 200, "body": {"orders": [{"id": "test-order-1", "status": "completed", "total": 299.99}]}}}, {"method": "GET", "path": "/api/orders/*", "response": {"status": 200, "body": {"id": "test-order-1", "status": "completed", "total": 299.99}}}]}, "mockPermissionService": {"port": 8081, "routes": [{"method": "GET", "path": "/permissions/check", "response": {"status": 200, "body": {"hasPermission": true, "permissions": ["read:orders", "write:orders"]}}}]}, "mockKeycloak": {"port": 8082, "routes": [{"method": "POST", "path": "/auth/realms/master/protocol/openid-connect/token", "response": {"status": 200, "body": {"access_token": "mock-access-token", "refresh_token": "mock-refresh-token", "token_type": "Bearer", "expires_in": 3600}}}, {"method": "GET", "path": "/auth/realms/master/protocol/openid-connect/certs", "response": {"status": 200, "body": {"keys": [{"kty": "RSA", "kid": "test-key-id", "use": "sig", "n": "test-n-value", "e": "AQAB", "k": "test-k-value"}]}}}]}}