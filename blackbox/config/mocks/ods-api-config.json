{"mockServer": {"port": 8080, "routes": [{"method": "GET", "path": "/api/orders", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"orders": [{"id": "test-order-1", "status": "completed", "total": 299.99, "createdAt": "2023-09-01T10:00:00Z", "updatedAt": "2023-09-01T11:00:00Z"}, {"id": "test-order-2", "status": "pending", "total": 159.5, "createdAt": "2023-09-01T09:00:00Z", "updatedAt": "2023-09-01T09:30:00Z"}]}}}, {"method": "GET", "path": "/api/orders/*", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"id": "test-order-1", "status": "completed", "total": 299.99, "items": [{"productId": "TEST-001", "quantity": 2, "price": 149.99}], "createdAt": "2023-09-01T10:00:00Z", "updatedAt": "2023-09-01T11:00:00Z"}}}, {"method": "POST", "path": "/api/orders", "response": {"status": 201, "headers": {"Content-Type": "application/json"}, "body": {"id": "test-order-3", "status": "created", "total": 0, "createdAt": "2023-09-01T12:00:00Z", "updatedAt": "2023-09-01T12:00:00Z"}}}]}}