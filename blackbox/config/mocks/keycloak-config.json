{"mockServer": {"port": 8082, "routes": [{"method": "POST", "path": "/auth/realms/master/protocol/openid-connect/token", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"access_token": "mock-access-token-12345", "refresh_token": "mock-refresh-token-67890", "token_type": "Bearer", "expires_in": 3600, "scope": "openid profile email"}}}, {"method": "GET", "path": "/auth/realms/master/protocol/openid-connect/certs", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"keys": [{"kty": "RSA", "kid": "test-key-id-123", "use": "sig", "n": "test-n-value-long-string-for-rsa-public-key", "e": "AQAB", "k": "test-k-value-long-string-for-rsa-public-key"}, {"kty": "RSA", "kid": "test-key-id-456", "use": "sig", "n": "test-n-value-another-long-string-for-rsa-public-key", "e": "AQAB", "k": "test-k-value-another-long-string-for-rsa-public-key"}]}}}, {"method": "GET", "path": "/auth/realms/master/protocol/openid-connect/userinfo", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"sub": "test-user-123", "name": "Test User", "given_name": "Test", "family_name": "User", "email": "<EMAIL>", "preferred_username": "testuser"}}}, {"method": "GET", "path": "/auth/realms/master/protocol/openid-connect/configuration", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"issuer": "http://localhost:8082/auth/realms/master", "authorization_endpoint": "http://localhost:8082/auth/realms/master/protocol/openid-connect/auth", "token_endpoint": "http://localhost:8082/auth/realms/master/protocol/openid-connect/token", "userinfo_endpoint": "http://localhost:8082/auth/realms/master/protocol/openid-connect/userinfo", "jwks_uri": "http://localhost:8082/auth/realms/master/protocol/openid-connect/certs", "scopes_supported": ["openid", "profile", "email"], "response_types_supported": ["code"], "grant_types_supported": ["authorization_code", "refresh_token"], "subject_types_supported": ["public"], "id_token_signing_alg_values_supported": ["RS256"], "token_endpoint_auth_methods_supported": ["client_secret_post", "private_key_jwt"]}}}]}}