{"name": "mcp-server-odi", "version": "0.1.0", "type": "module", "bin": {"mcp-server-odi": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js", "dev": "tsx watch src/index.ts", "test": "npm run test:type && npm run test:unit && npm run test:integration:simple", "test:type": "tsc --noEmit", "test:unit": "jest --testPathIgnorePatterns=tests/integration", "test:integration:simple": "jest tests/integration/simple-integration.test.ts --testTimeout=120000", "test:coverage": "jest --coverage --testPathIgnorePatterns=tests/integration", "test:blackbox": "make test-blackbox", "test:blackbox:fast": "make test-blackbox:fast", "test:blackbox:full": "make test-blackbox:full", "test:blackbox:oauth": "make test-blackbox:oauth", "test:mcp-core": "npm run test:mcp-core:unit && npm run test:mcp-core:blackbox", "test:mcp-core:unit": "jest tests/utils/event-store.test.ts tests/transport/http-transport.test.ts", "test:mcp-core:blackbox": "node blackbox/tests/mcp-core-blackbox-test.js", "test:ping": "jest tests/transport/http-transport.test.ts --testNamePattern=\"Ping|Health|Ready\"", "test:event-store": "jest tests/utils/event-store.test.ts", "test:tool-annotations": "jest tests/server/tool-annotations.test.ts", "test:oauth": "make test-oauth", "test:oauth:quick": "make test-oauth-quick", "test:oauth:config": "node blackbox/tests/oauth-server-blackbox-test.js", "test:oauth:endpoints": "node blackbox/tests/oauth-endpoints-test.js", "test:oauth:flow": "node blackbox/tests/oauth-flow-test.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "lint:staged": "lint-staged", "format": "prettier --write \"src/**/*.{ts,js,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json,md}\"", "nacos:validate": "node scripts/nacos-config-manager.js validate", "nacos:test": "node scripts/nacos-config-manager.js test-connection", "inspect": "npx @modelcontextprotocol/inspector tsx src/index.ts", "validate:env": "node scripts/validate-env.js", "config:oauth2-enabled": "node scripts/switch-auth-config.js --oauth2-enabled", "config:oauth2-disabled": "node scripts/switch-auth-config.js --oauth2-disabled", "config:status": "node scripts/switch-auth-config.js --status", "oauth:public": "node scripts/switch-oauth-mode.js public", "oauth:confidential": "node scripts/switch-oauth-mode.js confidential", "oauth:status": "node scripts/switch-oauth-mode.js", "prepare": "node -e \"if (process.env.CI || process.env.DOCKER_BUILD) { console.log('Skipping husky install in CI/Docker'); process.exit(0); } else { require('child_process').execSync('husky install', { stdio: 'inherit' }); }\"", "prestart": "npm run validate:env", "predev": "npm run validate:env"}, "files": ["dist"], "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "git add"], "*.{js,ts,tsx,json,md}": ["prettier --write", "git add"]}, "keywords": ["mcp"], "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "description": "mcp-server-odi", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "axios": "^1.11.0", "dotenv": "^17.2.1", "express": "^5.1.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "nacos": "^2.6.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.0.0", "@types/node-fetch": "^2.6.13", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "husky": "^8.0.3", "jest": "^30.0.5", "lint-staged": "^16.1.5", "node-fetch": "^3.3.2", "prettier": "^3.6.2", "shx": "^0.3.4", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "tsx": "^4.0.0", "typescript": "^5.0.0"}}