import { HttpMCPTransport } from '../../src/transport/http-transport.js';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import request from 'supertest';

// Mock dependencies
jest.mock('../../src/auth/oauth-provider.js', () => ({
  getOAuth2Config: jest.fn().mockReturnValue({
    enabled: true,
    server: { baseUrl: 'http://localhost:8080' },
    keycloak: { issuer: 'http://keycloak.test.com/realms/test' },
    client: { scopes: ['openid', 'profile'] },
  }),
}));

// Removed mcp-auth-setup.js mock - functionality moved to auth-middleware.js

jest.mock('../../src/auth/auth-middleware.js', () => ({
  createAuthMiddleware: jest.fn().mockReturnValue((_req: any, _res: any, next: any) => next()),
  getAuthStatus: jest.fn().mockReturnValue({ enabled: true, provider: 'oauth2' }),
  setupMCPAuth: jest.fn().mockReturnValue({
    oauthProvider: {
      verifyAccessToken: jest.fn().mockResolvedValue({
        clientId: 'test-client',
        scopes: ['openid'],
      }),
    },
  }),
  loadAuthConfig: jest.fn().mockReturnValue({
    connectionAuthEnabled: true,
    perRequestAuthEnabled: true,
  }),
  authenticate: jest.fn().mockResolvedValue({ success: true, authInfo: { clientId: 'test' } }),
}));

jest.mock('../../src/auth/auth-context.js', () => ({
  createRequestContextMiddleware: jest
    .fn()
    .mockReturnValue((_req: any, _res: any, next: any) => next()),
}));

jest.mock('../../src/config/global-config.js', () => ({
  getGlobalConfig: jest.fn().mockReturnValue({
    NODE_ENV: 'test',
    TRANSPORT: 'http',
    MCP_SERVER_PORT: 8080,
    OAUTH2_ENABLED: true,
  }),
}));

describe('HttpMCPTransport', () => {
  let transport: HttpMCPTransport;
  let mockServer: Server;

  beforeEach(() => {
    mockServer = {
      connect: jest.fn().mockResolvedValue(undefined),
    } as any;

    transport = new HttpMCPTransport(8080);
    transport.setMCPServer(mockServer);
  });

  afterEach(async () => {
    if (transport) {
      await transport.close();
    }
    jest.clearAllMocks();
  });

  describe('Transport Initialization', () => {
    it('should create transport with custom port when port is specified', () => {
      // Arrange: Set custom port value
      const customPort = 3000;

      // Act: Create transport with custom port
      const customTransport = new HttpMCPTransport(customPort);

      // Assert: Transport should use the specified port
      expect(customTransport.getPort()).toBe(customPort);
    });
  });

  describe('Server Lifecycle', () => {
    it('should start server successfully when valid configuration is provided', async () => {
      // Arrange: Transport is already configured in beforeEach

      // Act: Start the server
      const startPromise = transport.start();

      // Assert: Server should start without throwing errors
      await expect(startPromise).resolves.not.toThrow();
    });

    it('should handle server start errors when port is unavailable', async () => {
      // Arrange: Create transport and mock server to fail on start
      const errorTransport = new HttpMCPTransport(9999);
      const mockHttpServer = {
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === 'error') {
            setTimeout(() => handler(new Error('Port already in use')), 0);
          }
        }),
        close: jest.fn().mockImplementation(callback => callback && callback()),
      };
      errorTransport.setMCPServer(mockServer);
      errorTransport['app'].listen = jest.fn().mockReturnValue(mockHttpServer);

      // Act: Attempt to start server
      const startPromise = errorTransport.start();

      // Assert: Should throw port unavailable error
      await expect(startPromise).rejects.toThrow('Port already in use');

      // Cleanup
      await errorTransport.close();
    });
  });

  describe('Health Check', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return healthy status when server is running', async () => {
      // Arrange: Server is started in beforeEach

      // Act: Request health check endpoint
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should return healthy status with required fields
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('transport', 'streamable-http');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('activeSessions', 0);
      expect(response.body).toHaveProperty('sessions');
      expect(response.body).toHaveProperty('ping');
      expect(response.body).toHaveProperty('eventStore');
    });

    it('should return ping configuration in health check', async () => {
      // Act: Request health check endpoint
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should include ping configuration
      expect(response.body.ping).toHaveProperty('enabled');
      expect(response.body.ping).toHaveProperty('intervalMs');
      expect(response.body.ping).toHaveProperty('activePings');
      expect(typeof response.body.ping.enabled).toBe('boolean');
      expect(typeof response.body.ping.intervalMs).toBe('number');
      expect(typeof response.body.ping.activePings).toBe('number');
    });

    it('should return event store information in health check', async () => {
      // Act: Request health check endpoint
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should include event store information
      expect(response.body.eventStore).toHaveProperty('enabled', true);
      expect(response.body.eventStore).toHaveProperty('type', 'InMemoryEventStore');
      expect(response.body.eventStore).toHaveProperty('stats');
      expect(response.body.eventStore.stats).toHaveProperty('totalEvents');
      expect(response.body.eventStore.stats).toHaveProperty('totalStreams');
    });
  });

  describe('Ready Endpoint', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return ready status', async () => {
      // Act: Request ready endpoint
      const response = await request(transport['app']).get('/ready').expect(200);

      // Assert: Should return ready status with required fields
      expect(response.body).toHaveProperty('mode', 'stateful');
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('ready');
      expect(response.body).toHaveProperty('total', 1);
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('transport', 'streamable-http');
      expect(response.body).toHaveProperty('sessions');
    });

    it('should indicate ready when MCP server is set', async () => {
      // Arrange: Set MCP server
      const mockServer = { ping: jest.fn() } as any;
      transport.setMCPServer(mockServer);

      // Act: Request ready endpoint
      const response = await request(transport['app']).get('/ready').expect(200);

      // Assert: Should be ready
      expect(response.body.ready).toBe(1);
      expect(response.body.status).toBe('ready');
    });
  });

  describe('Ping Endpoint', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return pong response', async () => {
      // Act: Request ping endpoint
      const response = await request(transport['app']).get('/mcp/ping').expect(200);

      // Assert: Should return pong with timestamp
      expect(response.body).toHaveProperty('pong', true);
      expect(response.body).toHaveProperty('timestamp');
      expect(typeof response.body.timestamp).toBe('string');
    });
  });

  describe('Ping Configuration', () => {
    it('should configure ping settings', () => {
      // Act: Configure ping
      transport.configurePing(false, 60000);

      // Assert: Ping should be disabled with custom interval
      expect(transport['pingEnabled']).toBe(false);
      expect(transport['pingIntervalMs']).toBe(60000);
    });

    it('should use default ping settings', () => {
      // Assert: Default settings should be applied
      expect(transport['pingEnabled']).toBe(true);
      expect(transport['pingIntervalMs']).toBe(30000);
    });
  });

  describe('Session Management', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should track active sessions', () => {
      // Assert: Initially no sessions
      expect(transport.getActiveSessions()).toEqual([]);
      expect(transport.getSessionCount()).toBe(0);
    });

    it('should return correct port', () => {
      // Assert: Should return configured port
      expect(transport.getPort()).toBe(transport['port']);
    });
  });

  describe('Debug Configuration Access', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should hide debug config when environment is production', async () => {
      // Arrange: Set environment to production
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      // Act: Request debug config endpoint
      const response = await request(transport['app']).get('/debug/config').expect(404);

      // Assert: Should return not found error
      expect(response.body).toHaveProperty('error', 'Not found');

      // Cleanup
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('MCP Capabilities', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return MCP capabilities with OAuth2 support when OAuth2 is enabled', async () => {
      // Arrange: Server is started with OAuth2 enabled (mocked in setup)

      // Act: Request MCP capabilities
      const response = await request(transport['app']).get('/mcp/capabilities').expect(200);

      // Assert: Should return capabilities including OAuth2 support
      expect(response.body).toHaveProperty('transport', 'streamable-http');
      expect(response.body).toHaveProperty('streaming', true);
      expect(response.body).toHaveProperty('protocols');
      expect(response.body.protocols).toContain('jsonrpc-2.0');
      expect(response.body).toHaveProperty('oauth2_enabled', true);
      expect(response.body.oauth2).toHaveProperty('protected_resource_url');
    });
  });

  describe('Cross-Origin Resource Sharing', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should include CORS headers when handling cross-origin requests', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make a cross-origin request
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should include proper CORS headers
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toContain('GET');
      expect(response.headers['access-control-allow-methods']).toContain('POST');
    });

    it('should handle OPTIONS preflight requests correctly', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make an OPTIONS preflight request
      const response = await request(transport['app'])
        .options('/health')
        .set('Origin', 'https://example.com')
        .set('Access-Control-Request-Method', 'GET')
        .set('Access-Control-Request-Headers', 'Content-Type, Authorization')
        .expect(200);

      // Assert: Should include proper CORS preflight headers
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toBe('GET, POST, DELETE, OPTIONS');
      expect(response.headers['access-control-allow-headers']).toContain('Content-Type');
      expect(response.headers['access-control-allow-headers']).toContain('Authorization');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-session-id');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-protocol-version');
    });

    it('should expose required headers for MCP clients', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make a request that should expose MCP headers
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should expose MCP-specific headers
      expect(response.headers['access-control-expose-headers']).toContain('mcp-session-id');
      expect(response.headers['access-control-expose-headers']).toContain('mcp-protocol-version');
    });

    it('should include CORS headers on all endpoints', async () => {
      // Test multiple endpoints to ensure CORS is applied globally
      const endpoints = ['/health', '/auth/status', '/mcp/capabilities'];

      for (const endpoint of endpoints) {
        // Act: Make request to each endpoint
        const response = await request(transport['app']).get(endpoint);

        // Assert: Should include CORS headers regardless of status code
        expect(response.headers['access-control-allow-origin']).toBe('*');
        expect(response.headers['access-control-allow-methods']).toBe('GET, POST, DELETE, OPTIONS');
      }
    });

    it('should handle CORS for OAuth2 discovery endpoints', async () => {
      // Arrange: Server with OAuth2 enabled
      const oauth2Transport = new HttpMCPTransport(8081);
      await oauth2Transport.start();

      try {
        // Act: Make request to OAuth2 discovery endpoint
        const response = await request(oauth2Transport['app'])
          .get('/.well-known/oauth-protected-resource')
          .expect(200);

        // Assert: Should include CORS headers for OAuth2 discovery
        expect(response.headers['access-control-allow-origin']).toBe('*');
        expect(response.headers['access-control-allow-methods']).toContain('GET');
        expect(response.headers['access-control-expose-headers']).toContain('mcp-session-id');
        expect(response.headers['access-control-expose-headers']).toContain('mcp-protocol-version');
      } finally {
        await oauth2Transport.stop();
      }
    });

    it('should handle complex CORS scenarios with custom headers', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make a request with custom headers that should be allowed
      const response = await request(transport['app'])
        .get('/health')
        .set('Origin', 'https://mcp-client.example.com')
        .set('X-Requested-With', 'XMLHttpRequest')
        .set('mcp-session-id', 'test-session-123')
        .set('mcp-protocol-version', '1.0.0')
        .expect(200);

      // Assert: Should handle custom headers properly
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-headers']).toContain('X-Requested-With');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-session-id');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-protocol-version');
    });
  });

  describe('OAuth2 Discovery', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should serve OAuth2 protected resource metadata when OAuth2 is enabled', async () => {
      // Arrange: Server is started with OAuth2 enabled

      // Act: Request OAuth2 protected resource metadata
      const response = await request(transport['app'])
        .get('/.well-known/oauth-protected-resource')
        .expect(200);

      // Assert: Should return OAuth2 resource metadata
      expect(response.body).toHaveProperty('resource');
      expect(response.body).toHaveProperty('authorization_servers');
      expect(response.body).toHaveProperty('scopes_supported');
    });
  });

  describe('Session Ping Safeguard', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should start ping for session when onsessioninitialized callback fails to trigger', async () => {
      // Arrange: Set up MCP server with proper methods
      const mockServer = {
        ping: jest.fn(),
        connect: jest.fn(),
      };

      // Set the MCP server
      transport.setMCPServer(mockServer as any);

      // Spy on startPingForSession to verify it's called by the safeguard
      const startPingSpy = jest.spyOn(transport as any, 'startPingForSession');

      // Create a mock transport that simulates the scenario where onsessioninitialized fails
      const mockTransport = {
        sessionId: 'test-session-id', // Session ID is set by MCP SDK
        handleRequest: jest.fn().mockResolvedValue(undefined),
      };

      // Manually set up the scenario that the safeguard is supposed to handle:
      // 1. Transport has a session ID (simulating MCP SDK connection)
      // 2. Transport is in the transports collection (simulating partial session setup)
      // 3. No ping interval is set up (simulating onsessioninitialized failing to start ping)
      (transport as any).transports['test-session-id'] = mockTransport;
      (transport as any).pingIntervals = {}; // Empty - no ping interval set up

      // Act: Manually trigger the safeguard logic (this is what happens at the end of handleMCPPost)
      if (mockTransport.sessionId && !(transport as any).pingIntervals[mockTransport.sessionId]) {
        (transport as any).startPingForSession(mockTransport.sessionId, mockTransport);
      }

      // Assert: Verify that ping was started by the safeguard
      expect((transport as any).pingIntervals['test-session-id']).toBeDefined();
      expect(startPingSpy).toHaveBeenCalledTimes(1);
      expect(startPingSpy).toHaveBeenCalledWith('test-session-id', mockTransport);
    });
  });

  describe('Request Error Handling', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return bad request error when JSON payload is malformed', async () => {
      // Arrange: Prepare malformed JSON payload
      const malformedJson = 'invalid json';

      // Act: Send malformed JSON to MCP endpoint
      const response = await request(transport['app'])
        .post('/mcp')
        .set('Content-Type', 'application/json')
        .send(malformedJson)
        .expect(400);

      // Assert: Should return bad request error
      expect(response.text).toBeDefined();
    });

    it('should return bad request error when session ID is missing', async () => {
      // Arrange: No session ID provided in request

      // Act: Request MCP endpoint without session ID
      const response = await request(transport['app']).get('/mcp').expect(400);

      // Assert: Should return missing session ID error
      expect(response.text).toContain('Invalid or missing session ID');
    });

    it('should return SDK-compliant error when session ID does not exist', async () => {
      // Arrange: Use a non-existent session ID (following SDK pattern)
      const fakeSessionId = 'fake-session-id-12345';

      // Act: POST request with non-existent session ID
      const response = await request(transport['app'])
        .post('/mcp')
        .set('mcp-session-id', fakeSessionId)
        .send({ method: 'tools/list', jsonrpc: '2.0', id: 1 });

      // Assert: Should return SDK-compliant error message
      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        jsonrpc: '2.0',
        error: {
          code: -32000,
          message: 'Bad Request: No valid session ID provided',
        },
        id: null,
      });
    });

    it('should return SDK-compliant error for non-initialize requests without session', async () => {
      // Arrange: No session ID for a non-initialize request (following SDK pattern)

      // Act: POST request without session ID for tools/list
      const response = await request(transport['app'])
        .post('/mcp')
        .send({ method: 'tools/list', jsonrpc: '2.0', id: 1 })
        .expect(400);

      // Assert: Should return SDK-compliant error message
      expect(response.body).toMatchObject({
        jsonrpc: '2.0',
        error: {
          code: -32000,
          message: 'Bad Request: No valid session ID provided',
        },
        id: null,
      });
    });
  });
});
